# 工具调用系统设计

## 工具注册表

### 场景化工具注册

```typescript
/**
 * 场景化工具注册表
 */
export class ScenarioToolRegistry {
  private tools: Map<string, ScenarioTool> = new Map();
  private scenarioToolMappings: Map<ScenarioType, string[]> = new Map();

  constructor() {
    this.registerBuiltinTools();
    this.setupScenarioMappings();
  }

  /**
   * 注册内置工具
   */
  private registerBuiltinTools(): void {
    // 代码验证工具
    this.register(new CodeValidatorTool());
    this.register(new SyntaxCheckerTool());
    this.register(new LintTool());

    // 代码生成工具
    this.register(new CodeTemplateTool());
    this.register(new FrameworkHelperTool());
    this.register(new BestPracticesTool());

    // 文档查询工具
    this.register(new DocumentationLookupTool());
    this.register(new APIReferenceTool());
    this.register(new PatternDetectorTool());

    // 图表工具
    this.register(new MermaidValidatorTool());
    this.register(new DiagramOptimizerTool());

    // 搜索工具
    this.register(new SearchFilterTool());
    this.register(new RelevanceScorerTool());
    this.register(new ContextExpanderTool());
  }

  /**
   * 设置场景工具映射
   */
  private setupScenarioMappings(): void {
    this.scenarioToolMappings.set(ScenarioType.CODE_EDITING, [
      "code-validator", "syntax-checker", "lint"
    ]);

    this.scenarioToolMappings.set(ScenarioType.CODE_GENERATION, [
      "code-template", "framework-helper", "best-practices"
    ]);

    this.scenarioToolMappings.set(ScenarioType.CODE_ANALYSIS, [
      "documentation-lookup", "api-reference", "pattern-detector"
    ]);

    this.scenarioToolMappings.set(ScenarioType.DIAGRAM_GENERATION, [
      "mermaid-validator", "diagram-optimizer"
    ]);

    this.scenarioToolMappings.set(ScenarioType.RETRIEVAL_AUGMENTED, [
      "search-filter", "relevance-scorer", "context-expander"
    ]);
  }

  /**
   * 获取场景相关工具
   */
  getToolsForScenario(scenario: ScenarioType): ScenarioTool[] {
    const toolNames = this.scenarioToolMappings.get(scenario) || [];
    return toolNames.map(name => this.tools.get(name)).filter(Boolean) as ScenarioTool[];
  }
}
```

### 工具基类定义

```typescript
/**
 * 场景工具基类
 */
export abstract class ScenarioTool implements UnifiedTool {
  abstract name: string;
  abstract description: string;
  abstract parameters: JSONSchema;
  abstract supportedScenarios: ScenarioType[];

  /**
   * 执行工具
   */
  abstract execute(args: Record<string, any>, context: ToolExecutionContext): Promise<ToolResult>;

  /**
   * 验证工具是否适用于场景
   */
  isApplicableToScenario(scenario: ScenarioType): boolean {
    return this.supportedScenarios.includes(scenario);
  }

  /**
   * 获取场景特定的配置
   */
  getScenarioConfig(scenario: ScenarioType): ToolScenarioConfig | null {
    return null;
  }
}
```

## 具体工具实现

### 代码验证工具

```typescript
/**
 * 代码验证工具
 */
export class CodeValidatorTool extends ScenarioTool {
  name = "code-validator";
  description = "验证代码的语法正确性和基本质量";
  supportedScenarios = [ScenarioType.CODE_EDITING, ScenarioType.CODE_GENERATION];
  
  parameters: JSONSchema = {
    type: "object",
    properties: {
      code: {
        type: "string",
        description: "要验证的代码"
      },
      language: {
        type: "string",
        description: "编程语言",
        enum: ["typescript", "javascript", "python", "java", "go"]
      },
      validationLevel: {
        type: "string",
        description: "验证级别",
        enum: ["syntax", "basic", "strict"],
        default: "basic"
      }
    },
    required: ["code", "language"]
  };

  async execute(
    args: { code: string; language: string; validationLevel?: string },
    context: ToolExecutionContext
  ): Promise<ToolResult> {
    
    const { code, language, validationLevel = "basic" } = args;
    
    try {
      const validationResult = await this.validateCode(code, language, validationLevel);
      
      return {
        success: true,
        data: validationResult,
        message: validationResult.isValid 
          ? "代码验证通过" 
          : `发现 ${validationResult.errors.length} 个问题`
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "验证失败",
        message: "代码验证过程中出现错误"
      };
    }
  }

  private async validateCode(
    code: string, 
    language: string, 
    level: string
  ): Promise<ValidationResult> {
    switch (language) {
      case "typescript":
        return this.validateTypeScript(code, level);
      case "python":
        return this.validatePython(code, level);
      default:
        return { isValid: true, errors: [], warnings: [] };
    }
  }

  private async validateTypeScript(code: string, level: string): Promise<ValidationResult> {
    // TypeScript验证逻辑实现
    return { isValid: true, errors: [], warnings: [] };
  }

  private async validatePython(code: string, level: string): Promise<ValidationResult> {
    // Python验证逻辑实现
    return { isValid: true, errors: [], warnings: [] };
  }
}
```

### Mermaid图表验证工具

```typescript
/**
 * Mermaid图表验证工具
 */
export class MermaidValidatorTool extends ScenarioTool {
  name = "mermaid-validator";
  description = "验证Mermaid图表语法的正确性";
  supportedScenarios = [ScenarioType.DIAGRAM_GENERATION];
  
  parameters: JSONSchema = {
    type: "object",
    properties: {
      diagram: {
        type: "string",
        description: "Mermaid图表代码"
      },
      diagramType: {
        type: "string",
        description: "图表类型",
        enum: ["flowchart", "classDiagram", "sequenceDiagram", "gantt"]
      }
    },
    required: ["diagram"]
  };

  async execute(
    args: { diagram: string; diagramType?: string },
    context: ToolExecutionContext
  ): Promise<ToolResult> {
    
    const { diagram, diagramType } = args;
    
    try {
      const validation = await this.validateMermaidDiagram(diagram, diagramType);
      
      if (validation.isValid) {
        return {
          success: true,
          data: {
            isValid: true,
            optimizedDiagram: validation.optimizedDiagram,
            suggestions: validation.suggestions
          },
          message: "Mermaid图表语法正确"
        };
      } else {
        return {
          success: false,
          data: {
            isValid: false,
            errors: validation.errors,
            suggestions: validation.suggestions
          },
          message: `图表语法有 ${validation.errors.length} 个错误`
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "验证失败",
        message: "图表验证过程中出现错误"
      };
    }
  }

  private async validateMermaidDiagram(
    diagram: string,
    type?: string
  ): Promise<MermaidValidationResult> {
    // 实现Mermaid语法验证逻辑
    // 可以使用mermaid库或自定义解析器
    return {
      isValid: true,
      errors: [],
      suggestions: [],
      optimizedDiagram: diagram
    };
  }
}
```

### 代码模板工具

```typescript
/**
 * 代码模板工具
 */
export class CodeTemplateTool extends ScenarioTool {
  name = "code-template";
  description = "提供常用代码模板和脚手架";
  supportedScenarios = [ScenarioType.CODE_GENERATION];
  
  parameters: JSONSchema = {
    type: "object",
    properties: {
      templateType: {
        type: "string",
        description: "模板类型",
        enum: ["class", "function", "component", "test", "config"]
      },
      language: {
        type: "string",
        description: "编程语言",
        enum: ["typescript", "javascript", "python", "java", "go"]
      },
      framework: {
        type: "string",
        description: "框架或库",
        enum: ["react", "vue", "angular", "express", "fastapi", "spring"]
      },
      customOptions: {
        type: "object",
        description: "自定义选项",
        properties: {
          includeTests: { type: "boolean" },
          includeDocumentation: { type: "boolean" },
          styleGuide: { type: "string", enum: ["standard", "airbnb", "google"] }
        }
      }
    },
    required: ["templateType", "language"]
  };

  async execute(
    args: { 
      templateType: string; 
      language: string; 
      framework?: string; 
      customOptions?: any 
    },
    context: ToolExecutionContext
  ): Promise<ToolResult> {
    
    const { templateType, language, framework, customOptions = {} } = args;
    
    try {
      const template = await this.generateTemplate(
        templateType, 
        language, 
        framework, 
        customOptions
      );
      
      return {
        success: true,
        data: {
          template: template.code,
          description: template.description,
          usage: template.usage,
          dependencies: template.dependencies
        },
        message: `生成了 ${templateType} 模板`
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "模板生成失败",
        message: "代码模板生成过程中出现错误"
      };
    }
  }

  private async generateTemplate(
    type: string,
    language: string,
    framework?: string,
    options: any = {}
  ): Promise<CodeTemplate> {
    // 根据参数生成相应的代码模板
    const templateKey = `${language}-${type}-${framework || 'vanilla'}`;
    
    // 这里可以从预定义模板库中获取或动态生成
    return {
      code: this.getTemplateCode(templateKey, options),
      description: `${language} ${type} template`,
      usage: "Replace placeholders with your specific implementation",
      dependencies: this.getTemplateDependencies(templateKey)
    };
  }

  private getTemplateCode(templateKey: string, options: any): string {
    // 实现模板代码生成逻辑
    return "// Generated template code";
  }

  private getTemplateDependencies(templateKey: string): string[] {
    // 返回模板所需的依赖
    return [];
  }
}
```

## 工具执行上下文

### 执行上下文接口

```typescript
/**
 * 工具执行上下文
 */
export interface ToolExecutionContext {
  // 模板信息
  template: ConversationTemplate;
  scenario: ScenarioType;
  
  // 变量和数据
  variables: Record<string, any>;
  selectedText?: string;
  activeFile?: string;
  workspaceRoot?: string;
  
  // 服务和管理器
  logger: ILogger;
  configManager: IConfigurationManager;
  fileSystem: IFileSystemService;
  
  // 执行元数据
  executionId: string;
  timestamp: Date;
  userPreferences: UserPreferences;
}

/**
 * 工具执行结果
 */
export interface ToolResult {
  success: boolean;
  data?: any;
  error?: string;
  message: string;
  metadata?: Record<string, any>;
}

/**
 * 工具配置
 */
export interface ToolScenarioConfig {
  priority: number;
  timeout: number;
  retryCount: number;
  customSettings: Record<string, any>;
}
```

## 工具管理器

### 工具生命周期管理

```typescript
/**
 * 工具管理器
 */
export class ToolManager {
  private registry: ScenarioToolRegistry;
  private executionQueue: ToolExecutionQueue;
  private monitor: ToolMonitor;

  constructor(
    registry: ScenarioToolRegistry,
    monitor: ToolMonitor
  ) {
    this.registry = registry;
    this.executionQueue = new ToolExecutionQueue();
    this.monitor = monitor;
  }

  /**
   * 执行工具
   */
  async executeTool(
    toolName: string,
    args: Record<string, any>,
    context: ToolExecutionContext
  ): Promise<ToolResult> {
    
    const tool = this.registry.getTool(toolName);
    if (!tool) {
      throw new Error(`Tool '${toolName}' not found`);
    }

    // 验证工具是否适用于当前场景
    if (!tool.isApplicableToScenario(context.scenario)) {
      throw new Error(`Tool '${toolName}' not applicable to scenario '${context.scenario}'`);
    }

    // 验证参数
    const validation = this.validateToolArgs(tool, args);
    if (!validation.isValid) {
      throw new Error(`Invalid arguments: ${validation.errors.join(', ')}`);
    }

    // 执行工具
    const startTime = Date.now();
    this.monitor.recordToolStart(toolName, context);

    try {
      const result = await tool.execute(args, context);
      
      this.monitor.recordToolSuccess(toolName, Date.now() - startTime, result);
      return result;
    } catch (error) {
      this.monitor.recordToolError(toolName, Date.now() - startTime, error);
      throw error;
    }
  }

  /**
   * 批量执行工具
   */
  async executeToolChain(
    toolChain: ToolChainDefinition,
    context: ToolExecutionContext
  ): Promise<ToolChainResult> {
    
    const results: ToolResult[] = [];
    let chainContext = { ...context };

    for (const step of toolChain.steps) {
      try {
        const result = await this.executeTool(
          step.toolName,
          step.args,
          chainContext
        );
        
        results.push(result);
        
        // 更新链上下文
        if (step.outputMapping) {
          chainContext = this.updateChainContext(chainContext, result, step.outputMapping);
        }
        
        // 检查是否应该继续
        if (!result.success && step.stopOnFailure) {
          break;
        }
      } catch (error) {
        results.push({
          success: false,
          error: error instanceof Error ? error.message : String(error),
          message: `Tool '${step.toolName}' execution failed`
        });
        
        if (step.stopOnFailure) {
          break;
        }
      }
    }

    return {
      toolChain,
      results,
      success: results.every(r => r.success),
      finalContext: chainContext
    };
  }

  private validateToolArgs(tool: ScenarioTool, args: Record<string, any>): ValidationResult {
    // 实现参数验证逻辑
    return { isValid: true, errors: [] };
  }

  private updateChainContext(
    context: ToolExecutionContext,
    result: ToolResult,
    mapping: OutputMapping
  ): ToolExecutionContext {
    // 实现上下文更新逻辑
    return context;
  }
}
```
