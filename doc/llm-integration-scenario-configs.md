# 场景化配置系统详细设计

## 场景配置映射

### 核心场景配置

```typescript
/**
 * 场景化模板配置映射
 */
export const TEMPLATE_SCENARIO_CONFIGS: Record<string, ScenarioConfig> = {
  // Chat场景 - 基础对话
  "chat-en": {
    scenario: "conversational",
    primaryFunction: "general-chat",
    recommendedProviders: ["openai", "anthropic", "ollama"],
    providerPriority: {
      "openai": { model: "gpt-4o-mini", reason: "优秀的对话能力，成本效益好" },
      "anthropic": { model: "claude-3-haiku", reason: "自然对话，安全性高" },
      "ollama": { model: "llama3.1:8b", reason: "本地运行，隐私保护" }
    },
    requiredCapabilities: {
      chat: true,
      streaming: true,
      multiTurn: true,
      contextWindow: 4000
    },
    optionalCapabilities: {
      toolCalling: false,
      multimodal: false
    },
    defaultConfig: {
      temperature: 0.7,
      maxTokens: 1024,
      stop: ["Bot:", "Developer:"]
    }
  },

  // Task场景 - 代码编辑
  "edit-code": {
    scenario: "code-editing",
    primaryFunction: "code-modification",
    recommendedProviders: ["openai", "anthropic"],
    providerPriority: {
      "openai": { model: "gpt-4o", reason: "强大的代码理解和生成能力" },
      "anthropic": { model: "claude-3-sonnet", reason: "精确的代码分析能力" }
    },
    requiredCapabilities: {
      chat: true,
      streaming: true,
      codeGeneration: true,
      contextWindow: 8000,
      structuredOutput: true
    },
    optionalCapabilities: {
      toolCalling: true,
      multimodal: false
    },
    defaultConfig: {
      temperature: 0.1,
      maxTokens: 1536,
      stop: ["```"]
    },
    toolConfig: {
      enabledTools: ["code-validator", "syntax-checker"],
      toolSelectionStrategy: "auto"
    }
  },

  // Task场景 - 代码生成
  "generate-code": {
    scenario: "code-generation",
    primaryFunction: "code-creation",
    recommendedProviders: ["openai", "anthropic"],
    providerPriority: {
      "openai": { model: "gpt-4o", reason: "卓越的代码生成质量" },
      "anthropic": { model: "claude-3-sonnet", reason: "良好的代码结构设计" }
    },
    requiredCapabilities: {
      chat: true,
      streaming: true,
      codeGeneration: true,
      contextWindow: 8000
    },
    optionalCapabilities: {
      toolCalling: true,
      multimodal: false
    },
    defaultConfig: {
      temperature: 0.2,
      maxTokens: 2048,
      stop: ["```"]
    },
    toolConfig: {
      enabledTools: ["code-template", "framework-helper", "best-practices"],
      toolSelectionStrategy: "manual"
    }
  },

  // Task场景 - 代码解释
  "explain-code": {
    scenario: "code-analysis",
    primaryFunction: "code-explanation",
    recommendedProviders: ["anthropic", "openai", "ollama"],
    providerPriority: {
      "anthropic": { model: "claude-3-sonnet", reason: "优秀的代码分析和解释能力" },
      "openai": { model: "gpt-4o", reason: "全面的代码理解" },
      "ollama": { model: "deepseek-coder:6.7b", reason: "专业代码模型，本地运行" }
    },
    requiredCapabilities: {
      chat: true,
      streaming: true,
      codeAnalysis: true,
      contextWindow: 16000
    },
    optionalCapabilities: {
      toolCalling: true,
      multimodal: false
    },
    defaultConfig: {
      temperature: 0.3,
      maxTokens: 2048,
      stop: ["Developer:"]
    },
    toolConfig: {
      enabledTools: ["documentation-lookup", "api-reference", "pattern-detector"],
      toolSelectionStrategy: "auto"
    }
  },

  // Fun场景 - 图表生成
  "function-call-graph": {
    scenario: "diagram-generation",
    primaryFunction: "visual-analysis",
    recommendedProviders: ["anthropic", "openai"],
    providerPriority: {
      "anthropic": { model: "claude-3-sonnet", reason: "优秀的结构化分析和图表生成" },
      "openai": { model: "gpt-4o", reason: "强大的代码关系分析能力" }
    },
    requiredCapabilities: {
      chat: true,
      streaming: true,
      structuredOutput: true,
      codeAnalysis: true,
      contextWindow: 12000
    },
    optionalCapabilities: {
      toolCalling: true,
      multimodal: false
    },
    defaultConfig: {
      temperature: 0.3,
      maxTokens: 2048,
      stop: ["Function Graph Analyzer:", "Developer:"]
    },
    toolConfig: {
      enabledTools: ["mermaid-validator", "diagram-optimizer"],
      toolSelectionStrategy: "auto"
    }
  },

  // Experimental场景 - RAG检索
  "find-code-codebeat": {
    scenario: "retrieval-augmented",
    primaryFunction: "knowledge-retrieval",
    recommendedProviders: ["openai", "anthropic"],
    providerPriority: {
      "openai": { model: "gpt-4o", reason: "优秀的信息整合和检索能力" },
      "anthropic": { model: "claude-3-sonnet", reason: "精确的信息分析" }
    },
    requiredCapabilities: {
      chat: true,
      streaming: true,
      embedding: true,
      contextWindow: 16000,
      informationSynthesis: true
    },
    optionalCapabilities: {
      toolCalling: true,
      multimodal: false
    },
    defaultConfig: {
      temperature: 0.2,
      maxTokens: 2048,
      stop: ["Bot:", "Developer:"]
    },
    toolConfig: {
      enabledTools: ["search-filter", "relevance-scorer", "context-expander"],
      toolSelectionStrategy: "auto"
    },
    ragConfig: {
      embeddingModel: "text-embedding-ada-002",
      chunkSize: 1000,
      overlapSize: 200,
      maxRetrievedChunks: 5
    }
  }
};
```

## 场景配置接口

### 核心接口定义

```typescript
/**
 * 场景配置接口
 */
export interface ScenarioConfig {
  scenario: ScenarioType;
  primaryFunction: string;
  recommendedProviders: string[];
  providerPriority: Record<string, ProviderOption>;
  requiredCapabilities: RequiredCapabilities;
  optionalCapabilities: OptionalCapabilities;
  defaultConfig: DefaultLLMConfig;
  toolConfig?: ToolConfiguration;
  ragConfig?: RAGConfiguration;
}

/**
 * 场景类型枚举
 */
export enum ScenarioType {
  CONVERSATIONAL = "conversational",
  CODE_EDITING = "code-editing", 
  CODE_GENERATION = "code-generation",
  CODE_ANALYSIS = "code-analysis",
  DIAGRAM_GENERATION = "diagram-generation",
  RETRIEVAL_AUGMENTED = "retrieval-augmented",
  ERROR_DIAGNOSIS = "error-diagnosis",
  DOCUMENTATION = "documentation"
}

/**
 * 提供商选项
 */
export interface ProviderOption {
  model: string;
  reason: string;
  fallbackModel?: string;
  customConfig?: Record<string, any>;
}

/**
 * 必需能力
 */
export interface RequiredCapabilities {
  chat: boolean;
  streaming: boolean;
  contextWindow: number;
  codeGeneration?: boolean;
  codeAnalysis?: boolean;
  structuredOutput?: boolean;
  embedding?: boolean;
  multiTurn?: boolean;
  informationSynthesis?: boolean;
}

/**
 * 可选能力
 */
export interface OptionalCapabilities {
  toolCalling: boolean;
  multimodal: boolean;
  functionCalling?: boolean;
  imageGeneration?: boolean;
  audioProcessing?: boolean;
}

/**
 * 工具配置
 */
export interface ToolConfiguration {
  enabledTools: string[];
  toolSelectionStrategy: "auto" | "manual" | "hybrid";
  maxToolCalls?: number;
  toolTimeout?: number;
  customTools?: CustomToolDefinition[];
}

/**
 * RAG配置
 */
export interface RAGConfiguration {
  embeddingModel: string;
  chunkSize: number;
  overlapSize: number;
  maxRetrievedChunks: number;
  similarityThreshold?: number;
  rerankingEnabled?: boolean;
}
```

## 配置解析器

### 场景感知配置解析

```typescript
/**
 * 场景感知的模板配置解析器
 */
export class ScenarioConfigResolver {
  private scenarioConfigs: Map<string, ScenarioConfig>;
  private providerRegistry: IProviderRegistry;
  private userPreferences: UserPreferences;

  /**
   * 解析模板的最优配置
   */
  async resolveOptimalConfig(
    template: ConversationTemplate
  ): Promise<ResolvedTemplateConfig> {
    
    // 1. 确定场景配置
    const scenarioConfig = this.determineScenarioConfig(template);
    
    // 2. 解析提供商配置
    const providerConfig = await this.resolveProviderConfig(scenarioConfig);
    
    // 3. 解析工具配置
    const toolConfig = await this.resolveToolConfig(scenarioConfig, providerConfig);
    
    // 4. 解析LLM参数
    const llmParams = this.resolveLLMParameters(template, scenarioConfig);
    
    // 5. 解析RAG配置
    const ragConfig = this.resolveRAGConfig(template, scenarioConfig);

    return {
      templateId: template.id,
      scenarioConfig,
      providerConfig,
      toolConfig,
      llmParams,
      ragConfig,
      metadata: {
        resolvedAt: new Date().toISOString(),
        resolverVersion: "2.0",
        optimizationLevel: "scenario-aware"
      }
    };
  }

  /**
   * 确定场景配置
   */
  private determineScenarioConfig(template: ConversationTemplate): ScenarioConfig {
    // 1. 直接ID匹配
    if (this.scenarioConfigs.has(template.id)) {
      return this.scenarioConfigs.get(template.id)!;
    }

    // 2. 标签匹配
    const matchedByTags = this.matchByTags(template.tags || []);
    if (matchedByTags) {
      return matchedByTags;
    }

    // 3. 完成处理器匹配
    const matchedByHandler = this.matchByCompletionHandler(
      template.response.completionHandler
    );
    if (matchedByHandler) {
      return matchedByHandler;
    }

    // 4. 默认对话场景
    return this.scenarioConfigs.get("chat-en")!;
  }

  /**
   * 基于标签匹配场景
   */
  private matchByTags(tags: string[]): ScenarioConfig | null {
    const tagScenarioMap: Record<string, string> = {
      "edit": "edit-code",
      "generate": "generate-code", 
      "debug": "explain-code",
      "understand": "explain-code",
      "diagram": "function-call-graph",
      "fun": "function-call-graph"
    };

    for (const tag of tags) {
      const scenarioId = tagScenarioMap[tag];
      if (scenarioId && this.scenarioConfigs.has(scenarioId)) {
        return this.scenarioConfigs.get(scenarioId)!;
      }
    }

    return null;
  }
}
```
