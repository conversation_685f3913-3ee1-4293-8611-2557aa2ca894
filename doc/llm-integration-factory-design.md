# LLM工厂模式设计详解

## 场景感知LLM工厂

### 核心工厂实现

```typescript
/**
 * 场景感知的LLM工厂
 */
export class ScenarioAwareLLMFactory extends LLMFactory {
  private scenarioConfigs: Map<string, ScenarioConfig>;
  private providerCapabilityMatrix: ProviderCapabilityMatrix;
  private toolRegistry: ToolRegistry;

  constructor(config: ScenarioAwareLLMFactoryConfig) {
    super(config);
    this.scenarioConfigs = new Map(Object.entries(TEMPLATE_SCENARIO_CONFIGS));
    this.providerCapabilityMatrix = new ProviderCapabilityMatrix();
    this.toolRegistry = new ToolRegistry();
  }

  /**
   * 为特定模板创建最优LLM客户端
   */
  async createOptimalClientForTemplate(
    templateId: string,
    template: ConversationTemplate,
    userPreferences?: UserPreferences
  ): Promise<OptimalLLMClient> {
    
    // 1. 获取场景配置
    const scenarioConfig = this.getScenarioConfig(templateId, template);
    
    // 2. 评估可用提供商
    const availableProviders = await this.evaluateAvailableProviders(scenarioConfig);
    
    // 3. 选择最优提供商
    const selectedProvider = await this.selectOptimalProvider(
      scenarioConfig,
      availableProviders,
      userPreferences
    );
    
    // 4. 配置工具调用
    const toolConfig = await this.configureTools(scenarioConfig, selectedProvider);
    
    // 5. 创建优化的客户端
    return this.createOptimizedClient({
      provider: selectedProvider,
      scenarioConfig,
      toolConfig,
      template
    });
  }

  /**
   * 获取场景配置
   */
  private getScenarioConfig(
    templateId: string, 
    template: ConversationTemplate
  ): ScenarioConfig {
    // 1. 直接匹配模板ID
    if (this.scenarioConfigs.has(templateId)) {
      return this.scenarioConfigs.get(templateId)!;
    }

    // 2. 基于标签匹配
    const tags = template.tags || [];
    for (const [configId, config] of this.scenarioConfigs) {
      if (this.matchesTags(config, tags)) {
        return config;
      }
    }

    // 3. 基于完成处理器类型推断
    const handlerType = template.response.completionHandler?.type;
    if (handlerType) {
      const inferredConfig = this.inferScenarioFromHandler(handlerType);
      if (inferredConfig) {
        return inferredConfig;
      }
    }

    // 4. 默认对话场景
    return this.scenarioConfigs.get("chat-en")!;
  }

  /**
   * 评估可用提供商
   */
  private async evaluateAvailableProviders(
    scenarioConfig: ScenarioConfig
  ): Promise<ProviderEvaluation[]> {
    const evaluations: ProviderEvaluation[] = [];

    for (const providerId of scenarioConfig.recommendedProviders) {
      try {
        const provider = await this.createProvider(providerId, {});
        const capabilities = provider.capabilities;
        
        // 检查必需能力
        const meetsRequirements = this.checkRequiredCapabilities(
          capabilities,
          scenarioConfig.requiredCapabilities
        );

        if (meetsRequirements) {
          const evaluation: ProviderEvaluation = {
            providerId,
            provider,
            capabilityScore: this.calculateCapabilityScore(capabilities, scenarioConfig),
            availabilityScore: await this.checkProviderAvailability(provider),
            performanceScore: await this.getProviderPerformanceScore(providerId),
            costScore: this.calculateCostScore(providerId, scenarioConfig),
            totalScore: 0 // 将在后面计算
          };

          evaluation.totalScore = this.calculateTotalScore(evaluation);
          evaluations.push(evaluation);
        }
      } catch (error) {
        // 提供商不可用，跳过
        continue;
      }
    }

    return evaluations.sort((a, b) => b.totalScore - a.totalScore);
  }

  /**
   * 选择最优提供商
   */
  private async selectOptimalProvider(
    scenarioConfig: ScenarioConfig,
    evaluations: ProviderEvaluation[],
    userPreferences?: UserPreferences
  ): Promise<ILLMProvider> {
    
    if (evaluations.length === 0) {
      throw new Error(`No suitable providers found for scenario: ${scenarioConfig.scenario}`);
    }

    // 应用用户偏好
    if (userPreferences?.preferredProvider) {
      const preferredEvaluation = evaluations.find(
        e => e.providerId === userPreferences.preferredProvider
      );
      if (preferredEvaluation) {
        return preferredEvaluation.provider;
      }
    }

    // 应用场景优先级
    for (const [providerId, option] of Object.entries(scenarioConfig.providerPriority)) {
      const evaluation = evaluations.find(e => e.providerId === providerId);
      if (evaluation && evaluation.totalScore > 0.7) { // 阈值检查
        return evaluation.provider;
      }
    }

    // 返回评分最高的提供商
    return evaluations[0].provider;
  }

  /**
   * 配置工具调用
   */
  private async configureTools(
    scenarioConfig: ScenarioConfig,
    provider: ILLMProvider
  ): Promise<ToolConfiguration | null> {
    
    if (!scenarioConfig.toolConfig || !scenarioConfig.optionalCapabilities.toolCalling) {
      return null;
    }

    // 检查提供商是否支持工具调用
    if (!CapabilityChecker.supportsToolCallingStandard(
      provider.capabilities,
      ToolCallingStandard.OPENAI_TOOLS
    )) {
      return null;
    }

    // 加载和验证工具
    const availableTools = await this.loadTools(scenarioConfig.toolConfig.enabledTools);
    const validatedTools = await this.validateTools(availableTools, provider);

    return {
      ...scenarioConfig.toolConfig,
      enabledTools: validatedTools.map(t => t.name),
      toolDefinitions: validatedTools
    };
  }

  /**
   * 创建优化的客户端
   */
  private async createOptimizedClient(config: {
    provider: ILLMProvider;
    scenarioConfig: ScenarioConfig;
    toolConfig: ToolConfiguration | null;
    template: ConversationTemplate;
  }): Promise<OptimalLLMClient> {
    
    return new OptimalLLMClient({
      provider: config.provider,
      scenarioConfig: config.scenarioConfig,
      toolConfig: config.toolConfig,
      template: config.template,
      middleware: this.createScenarioMiddleware(config.scenarioConfig),
      monitor: this.createScenarioMonitor(config.scenarioConfig)
    });
  }

  /**
   * 检查必需能力
   */
  private checkRequiredCapabilities(
    capabilities: LLMCapabilities,
    requirements: RequiredCapabilities
  ): boolean {
    // 检查基础能力
    if (requirements.chat && !capabilities.chat) return false;
    if (requirements.streaming && !capabilities.streaming) return false;
    if (requirements.embedding && !capabilities.embedding) return false;

    // 检查上下文窗口
    if (requirements.contextWindow && capabilities.contextWindow < requirements.contextWindow) {
      return false;
    }

    // 检查代码相关能力
    if (requirements.codeGeneration && !this.hasCodeCapability(capabilities)) return false;
    if (requirements.codeAnalysis && !this.hasCodeCapability(capabilities)) return false;

    return true;
  }

  /**
   * 计算能力分数
   */
  private calculateCapabilityScore(
    capabilities: LLMCapabilities,
    scenarioConfig: ScenarioConfig
  ): number {
    let score = 0;
    let totalChecks = 0;

    // 基础能力评分
    const basicCapabilities = ['chat', 'streaming', 'embedding'];
    for (const cap of basicCapabilities) {
      totalChecks++;
      if (capabilities[cap as keyof LLMCapabilities]) {
        score++;
      }
    }

    // 场景特定能力评分
    if (scenarioConfig.requiredCapabilities.codeGeneration) {
      totalChecks++;
      if (this.hasCodeCapability(capabilities)) score++;
    }

    if (scenarioConfig.optionalCapabilities.toolCalling) {
      totalChecks++;
      if (capabilities.toolCalling.length > 0) score++;
    }

    return totalChecks > 0 ? score / totalChecks : 0;
  }

  /**
   * 检查提供商可用性
   */
  private async checkProviderAvailability(provider: ILLMProvider): Promise<number> {
    try {
      const healthStatus = await provider.healthCheck();
      return healthStatus.status === 'healthy' ? 1.0 : 0.5;
    } catch (error) {
      return 0.0;
    }
  }

  /**
   * 获取提供商性能分数
   */
  private async getProviderPerformanceScore(providerId: string): Promise<number> {
    // 从监控系统获取历史性能数据
    const metrics = await this.monitoringSystem.getProviderMetrics(providerId);
    
    if (!metrics) return 0.5; // 默认分数

    // 基于延迟、成功率、吞吐量计算综合分数
    const latencyScore = Math.max(0, 1 - (metrics.averageLatency / 5000)); // 5秒为基准
    const successScore = metrics.successRate;
    const throughputScore = Math.min(1, metrics.throughput / 100); // 100 req/min为基准

    return (latencyScore * 0.4) + (successScore * 0.4) + (throughputScore * 0.2);
  }

  /**
   * 计算成本分数
   */
  private calculateCostScore(providerId: string, scenarioConfig: ScenarioConfig): number {
    // 获取提供商定价信息
    const pricing = this.getPricingInfo(providerId);
    if (!pricing) return 0.5;

    // 估算场景的token使用量
    const estimatedTokens = this.estimateTokenUsage(scenarioConfig);
    const estimatedCost = pricing.inputTokenPrice * estimatedTokens.input + 
                         pricing.outputTokenPrice * estimatedTokens.output;

    // 成本越低分数越高（反比关系）
    const maxAcceptableCost = 0.01; // $0.01 per request
    return Math.max(0, 1 - (estimatedCost / maxAcceptableCost));
  }

  /**
   * 计算总分
   */
  private calculateTotalScore(evaluation: ProviderEvaluation): number {
    // 权重配置
    const weights = {
      capability: 0.4,
      availability: 0.3,
      performance: 0.2,
      cost: 0.1
    };

    return (
      evaluation.capabilityScore * weights.capability +
      evaluation.availabilityScore * weights.availability +
      evaluation.performanceScore * weights.performance +
      evaluation.costScore * weights.cost
    );
  }
}
```

## 提供商注册表

### 注册表实现

```typescript
/**
 * 提供商注册表 - 管理所有可用的LLM提供商
 */
export class ProviderRegistry implements IProviderRegistry {
  private providers: Map<string, ProviderConstructor> = new Map();
  private metadata: Map<string, ProviderMetadata> = new Map();

  /**
   * 注册提供商
   */
  register(id: string, providerClass: ProviderConstructor): void {
    if (this.providers.has(id)) {
      throw new Error(`Provider '${id}' already registered`);
    }

    // 验证提供商类
    this.validateProviderClass(providerClass);
    
    this.providers.set(id, providerClass);
    
    // 提取元数据
    const metadata = this.extractMetadata(providerClass);
    this.metadata.set(id, metadata);
  }

  /**
   * 获取提供商类
   */
  getProvider(id: string): ProviderConstructor | undefined {
    return this.providers.get(id);
  }

  /**
   * 获取所有注册的提供商
   */
  getAllProviders(): ProviderInfo[] {
    return Array.from(this.providers.keys()).map(id => ({
      id,
      metadata: this.metadata.get(id)!,
    }));
  }

  /**
   * 按能力筛选提供商
   */
  getProvidersByCapability(capability: keyof LLMCapabilities): ProviderInfo[] {
    return this.getAllProviders().filter(provider => 
      provider.metadata.capabilities[capability]
    );
  }

  /**
   * 验证提供商类是否符合接口要求
   */
  private validateProviderClass(providerClass: ProviderConstructor): void {
    const instance = new providerClass();
    
    // 检查必需属性
    const requiredProperties = ['id', 'name', 'version', 'capabilities'];
    for (const prop of requiredProperties) {
      if (!(prop in instance)) {
        throw new Error(`Provider class missing required property: ${prop}`);
      }
    }

    // 检查必需方法
    const requiredMethods = ['chat', 'embedding', 'initialize', 'dispose', 'healthCheck'];
    for (const method of requiredMethods) {
      if (typeof instance[method as keyof ILLMProvider] !== 'function') {
        throw new Error(`Provider class missing required method: ${method}`);
      }
    }
  }

  /**
   * 提取提供商元数据
   */
  private extractMetadata(providerClass: ProviderConstructor): ProviderMetadata {
    const instance = new providerClass();
    
    return {
      id: instance.id,
      name: instance.name,
      version: instance.version,
      capabilities: instance.capabilities,
      description: this.getProviderDescription(instance.id),
      supportedModels: this.getSupportedModels(instance.id),
      pricing: this.getPricingInfo(instance.id),
      documentation: this.getDocumentationUrl(instance.id)
    };
  }

  /**
   * 获取提供商描述
   */
  private getProviderDescription(providerId: string): string {
    const descriptions: Record<string, string> = {
      'openai': 'OpenAI GPT models with advanced capabilities',
      'anthropic': 'Anthropic Claude models with strong reasoning',
      'ollama': 'Local LLM models for privacy and control'
    };
    
    return descriptions[providerId] || 'LLM Provider';
  }

  /**
   * 获取支持的模型列表
   */
  private getSupportedModels(providerId: string): string[] {
    const modelMappings: Record<string, string[]> = {
      'openai': ['gpt-4o', 'gpt-4o-mini', 'gpt-3.5-turbo'],
      'anthropic': ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
      'ollama': ['llama3.1:8b', 'deepseek-coder:6.7b', 'qwen2.5:7b']
    };
    
    return modelMappings[providerId] || [];
  }
}
```

## 能力检查器

### 能力匹配工具

```typescript
/**
 * 能力检查工具类 - 提供便捷的能力查询方法
 */
export class CapabilityChecker {
  
  /**
   * 检查是否支持特定的多模态输入
   */
  static supportsMultimodalInput(
    capabilities: LLMCapabilities, 
    inputType: MultimodalInputType
  ): boolean {
    return capabilities.multimodalInput.includes(inputType);
  }
  
  /**
   * 检查是否支持多种输入模态的组合
   */
  static supportsMultimodalInputCombination(
    capabilities: LLMCapabilities,
    inputTypes: MultimodalInputType[]
  ): boolean {
    return inputTypes.every(type => 
      capabilities.multimodalInput.includes(type)
    );
  }
  
  /**
   * 检查是否支持特定的工具调用标准
   */
  static supportsToolCallingStandard(
    capabilities: LLMCapabilities,
    standard: ToolCallingStandard
  ): boolean {
    return capabilities.toolCalling.includes(standard);
  }
  
  /**
   * 获取最佳的工具调用标准
   */
  static getBestToolCallingStandard(
    capabilities: LLMCapabilities,
    preferredStandards: ToolCallingStandard[] = [
      ToolCallingStandard.OPENAI_TOOLS,
      ToolCallingStandard.LANGCHAIN_TOOLS,
      ToolCallingStandard.OPENAI_FUNCTIONS
    ]
  ): ToolCallingStandard | null {
    for (const preferred of preferredStandards) {
      if (capabilities.toolCalling.includes(preferred)) {
        return preferred;
      }
    }
    return capabilities.toolCalling[0] || null;
  }
  
  /**
   * 检查是否支持特定输出格式
   */
  static supportsOutputFormat(
    capabilities: LLMCapabilities,
    format: OutputFormat
  ): boolean {
    return capabilities.outputFormats.includes(format);
  }
  
  /**
   * 获取能力兼容性分数
   */
  static calculateCompatibilityScore(
    capabilities: LLMCapabilities,
    requirements: CapabilityRequirements
  ): number {
    let score = 0;
    let totalChecks = 0;
    
    // 检查基础能力
    if (requirements.chat !== undefined) {
      totalChecks++;
      if (capabilities.chat === requirements.chat) score++;
    }
    
    if (requirements.streaming !== undefined) {
      totalChecks++;
      if (capabilities.streaming === requirements.streaming) score++;
    }
    
    // 检查多模态输入要求
    if (requirements.requiredInputTypes?.length) {
      totalChecks++;
      if (this.supportsMultimodalInputCombination(capabilities, requirements.requiredInputTypes)) {
        score++;
      }
    }
    
    // 检查工具调用要求
    if (requirements.requiredToolStandards?.length) {
      totalChecks++;
      const hasAnyRequired = requirements.requiredToolStandards.some(standard =>
        capabilities.toolCalling.includes(standard)
      );
      if (hasAnyRequired) score++;
    }
    
    // 检查输出格式要求
    if (requirements.requiredOutputFormats?.length) {
      totalChecks++;
      const hasAllRequired = requirements.requiredOutputFormats.every(format =>
        capabilities.outputFormats.includes(format)
      );
      if (hasAllRequired) score++;
    }
    
    return totalChecks > 0 ? score / totalChecks : 0;
  }
}

/**
 * 能力需求定义
 */
export interface CapabilityRequirements {
  chat?: boolean;
  streaming?: boolean;
  embedding?: boolean;
  requiredInputTypes?: MultimodalInputType[];
  requiredOutputTypes?: MultimodalOutputType[];
  requiredToolStandards?: ToolCallingStandard[];
  requiredOutputFormats?: OutputFormat[];
  minContextWindow?: number;
  minMaxTokens?: number;
  requiresSystemPrompts?: boolean;
  requiresContentFiltering?: boolean;
}
```
