import { BaseChatModel } from "@langchain/core/language_models/chat_models";
import { BaseMessage } from "@langchain/core/messages";
import { ChatOllama } from "@langchain/ollama";
import { ChatOpenAI, OpenAIEmbeddings } from "@langchain/openai";
import { ConfigurationManager, ConfigUtils } from "../config/ConfigurationManager";
import { Logger } from "../logger";
import { ErrorHandler } from "../utils/ErrorHandler";
import { APIKeyManager } from "./ApiKeyManager";

// Type definitions for compatibility
export type Vector = number[];

export interface EmbeddingResult {
  type: "success" | "error";
  embedding?: Vector;
  totalTokenCount?: number;
  errorMessage?: string;
}

export interface ChatRequest {
  prompt: string;
  maxTokens: number;
  stop?: string[];
  temperature?: number;
}

export interface ChatResponse {
  content: string;
  done: boolean;
}

/**
 * AI Client implementation using LangChain
 *
 * This implementation completely replaces modelfusion with LangChain ecosystem:
 * - @langchain/openai for OpenAI models
 * - @langchain/ollama for local Ollama models
 * - Intelligent caching and configuration management
 * - Enhanced error handling and performance optimization
 *
 * Provides backward compatibility with the original AIClient interface.
 */
// Client cache interface for better type safety
interface ClientCache {
  openai?: ChatOpenAI;
  ollama?: ChatOllama;
  embedding?: OpenAIEmbeddings;
}



export class AIClient {
  private readonly apiKeyManager: APIKeyManager;
  private readonly logger: Logger;
  private readonly configManager: ConfigurationManager;
  private readonly clientCache: ClientCache = {};

  constructor({
    apiKeyManager,
    logger,
  }: {
    apiKeyManager: APIKeyManager;
    logger: Logger;
  }) {
    this.apiKeyManager = apiKeyManager;
    this.logger = logger;
    this.configManager = ConfigurationManager.getInstance(logger);
  }

  /**
   * Get the appropriate chat model based on configuration
   * Falls back to Ollama if OpenAI API key is not available
   */
  private async getChatModel(
    params?: { maxTokens?: number; stop?: string[]; temperature?: number }
  ): Promise<BaseChatModel> {
    try {
      const config = this.configManager.getConfiguration();
      const currentModel = config.model;

      // console.log("🤖 Getting chat model:", { modelConfiguration: currentModel, params });
      this.logger.debug([
        `🤖 Getting chat model:${{ modelConfiguration: currentModel, params }}`
      ]);

      // Check if it's an Ollama model
      if (ConfigUtils.isOllamaModel(currentModel)) {
        this.logger.debug([
          `🦙 Using Ollama model:'${currentModel}'`
        ]);
        return params
          ? this.getOllamaClientWithParams(currentModel, params as Required<typeof params>)
          : this.getOllamaClient(currentModel);
      }

      // For OpenAI models, check if API key is available
      const hasApiKey = await this.apiKeyManager.hasOpenAIApiKey();
      // console.log("🔑 OpenAI API key status:", { hasApiKey });
      this.logger.log(`🔑 OpenAI API key status: '${hasApiKey}'`);


      if (!hasApiKey) {
        this.logger.log([
          `OpenAI model '${currentModel}' requested but no API key configured.`,
          `Falling back to Ollama '${currentModel}' model.`,
          "To use OpenAI models, please set your API key using 'CodeBeat: Enter OpenAI API key' command."
        ]);

        // console.log(`🦙 Falling back to Ollama ${currentModel}`);
        this.logger.log(`🦙 Falling back to Ollama ${currentModel}`);

        // Fall back to Ollama model
        return params
          ? this.getOllamaClientWithParams(currentModel, params as Required<typeof params>)
          : this.getOllamaClient(currentModel);
      }

      // Use OpenAI model
      // console.log("🤖 Using OpenAI model:", currentModel);
      this.logger.log(`🤖 Using OpenAI model: '${currentModel}'`);
      return this.getOpenAIClient(currentModel, params);
    } catch (error) {
      // console.error("❌ Failed to get chat model:", error);
      this.logger.error(`❌ Failed to get chat model: ${error}`);
      throw error;
    }
  }



  private async getOpenAIClient(
    model: string,
    params?: { maxTokens?: number; stop?: string[]; temperature?: number }
  ): Promise<ChatOpenAI> {
    // If no params provided and we have a cached client, return it
    if (!params && this.clientCache.openai) {
      return this.clientCache.openai;
    }

    const apiKey = await this.apiKeyManager.getOpenAIApiKey();

    if (!apiKey) {
      throw new Error(
        "OpenAI API key not configured. " +
        "Please set your OpenAI API key using the 'CodeBeat: Enter OpenAI API key' command, " +
        "or switch to an Ollama model (deepseek-r1:1.5b, mistral, etc.) which runs locally without requiring an API key."
      );
    }

    const config = this.configManager.getConfiguration();

    const clientConfig = {
      apiKey,
      configuration: {
        baseURL: config.openaiBaseUrl,
      },
      model: model,
      temperature: params?.temperature ?? 0,
      ...(params?.maxTokens && { maxTokens: params.maxTokens }),
      ...(params?.stop && { stop: params.stop }),
    };

    const client = new ChatOpenAI(clientConfig);

    // Cache the client only if no specific params were provided
    if (!params) {
      this.clientCache.openai = client;
    }

    return client;
  }

  private getOllamaClient(model: string): ChatOllama {
    if (!this.clientCache.ollama) {
      const config = this.configManager.getConfiguration();
      const baseUrl = config.ollamaBaseUrl;

      // console.log("🦙 Creating Ollama client:", { model: model, baseUrl });
      this.logger.log(`🦙 Creating Ollama client:", ${{ model: model, baseUrl }}`)

      this.clientCache.ollama = new ChatOllama({
        baseUrl,
        model: model,
        temperature: 0,
      });

      // Test connection (async but don't wait for it)
      // this.testOllamaConnection(baseUrl, model);
    }

    return this.clientCache.ollama;
  }

  // private async testOllamaConnection(baseUrl: string, model: string): Promise<void> {
  //   try {
  //     console.log("🔍 Testing Ollama connection...", { baseUrl, model });

  //     // Simple fetch to check if Ollama is running
  //     const response = await fetch(`${baseUrl}/api/tags`);
  //     if (response.ok) {
  //       const data = await response.json();
  //       console.log("✅ Ollama is running, available models:", data.models?.map((m: any) => m.name) || []);

  //       // Check if the specific model is available
  //       const hasModel = data.models?.some((m: any) => m.name === model);
  //       if (!hasModel) {
  //         console.warn(`⚠️ Model '${model}' not found in Ollama. Available models:`, data.models?.map((m: any) => m.name));
  //         this.logger.log([
  //           `Warning: Model '${model}' not found in Ollama.`,
  //           `Available models: ${data.models?.map((m: any) => m.name).join(', ') || 'none'}`,
  //           `You may need to run: ollama pull ${model}`
  //         ]);
  //       }
  //     } else {
  //       throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  //     }
  //   } catch (error) {
  //     console.error("❌ Ollama connection test failed:", error);
  //     this.logger.log([
  //       `Warning: Cannot connect to Ollama at ${baseUrl}`,
  //       `Error: ${error instanceof Error ? error.message : String(error)}`,
  //       "Please ensure Ollama is running: https://ollama.ai/download"
  //     ]);
  //   }
  // }

  private getOllamaClientWithParams(
    model: string,
    params: { maxTokens: number; stop?: string[]; temperature?: number }
  ): ChatOllama {
    const config = this.configManager.getConfiguration();

    return new ChatOllama({
      baseUrl: config.ollamaBaseUrl,
      model: model,
      temperature: params.temperature ?? 0,
      numPredict: params.maxTokens, // Ollama uses numPredict for max tokens
      stop: params.stop,
    });
  }

  private async getEmbeddingClient(): Promise<OpenAIEmbeddings> {
    if (!this.clientCache.embedding) {
      const apiKey = await this.apiKeyManager.getOpenAIApiKey();

      if (!apiKey) {
        throw new Error(
          "OpenAI API key required for embedding generation. " +
          "Please set your OpenAI API key using the 'CodeBeat: Enter OpenAI API key' command. " +
          "Note: Embedding generation currently requires OpenAI and cannot use local Ollama models."
        );
      }

      const config = this.configManager.getConfiguration();
      this.clientCache.embedding = new OpenAIEmbeddings({
        apiKey,
        configuration: {
          baseURL: config.openaiBaseUrl,
        },
        model: "text-embedding-ada-002",
      });
    }

    return this.clientCache.embedding;
  }

  /**
   * Stream text generation - maintains compatibility with original interface
   */
  async chat({
    prompt,
    maxTokens,
    stop,
    temperature = 0,
  }: ChatRequest): Promise<AsyncIterable<string>> {
    this.logger.log(["--- Start prompt ---", prompt, "--- End prompt ---"]);

    const model = await this.getChatModel({ maxTokens, stop, temperature });

    const stream = await model.stream([
      { role: "user", content: prompt }
    ]);

    // Convert LangChain stream to string iterator for compatibility
    return this.convertStreamToStringIterator(stream);
  }

  private async *convertStreamToStringIterator(
    stream: AsyncIterable<BaseMessage>
  ): AsyncIterable<string> {
    for await (const chunk of stream) {
      if (chunk.content && typeof chunk.content === 'string') {
        yield chunk.content;
      }
    }
  }

  /**
   * Generate embeddings - maintains compatibility with original interface
   */
  async embedding({ input }: { input: string }): Promise<EmbeddingResult> {
    try {
      const embeddingClient = await this.getEmbeddingClient();

      const embedding = await embeddingClient.embedQuery(input);

      return {
        type: "success",
        embedding,
        totalTokenCount: undefined, // LangChain doesn't provide token count by default
      };
    } catch (error: unknown) {
      await ErrorHandler.handleError(
        error,
        'AIClient.generateEmbedding',
        { showToUser: false, logLevel: 'error' }
      );

      return {
        type: "error",
        errorMessage: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Clear all cached clients
   * Useful for testing or when configuration changes
   */
  clearCache(): void {
    this.clientCache.openai = undefined;
    this.clientCache.ollama = undefined;
    this.clientCache.embedding = undefined;
    this.configManager.clearCache();
  }

  /**
   * Get cache statistics for monitoring and debugging
   */
  getCacheStats(): {
    hasOpenAIClient: boolean;
    hasOllamaClient: boolean;
    hasEmbeddingClient: boolean;
    configCacheAge: number;
    cacheEfficiency: string;
  } {
    const configDiagnostics = this.configManager.getDiagnostics();

    // Calculate cache efficiency
    const activeClients = [
      !!this.clientCache.openai,
      !!this.clientCache.ollama,
      !!this.clientCache.embedding
    ].filter(Boolean).length;

    const efficiency = activeClients > 0 ?
      `${activeClients}/3 clients cached` :
      'No active cache';

    return {
      hasOpenAIClient: !!this.clientCache.openai,
      hasOllamaClient: !!this.clientCache.ollama,
      hasEmbeddingClient: !!this.clientCache.embedding,
      configCacheAge: configDiagnostics.cacheAge,
      cacheEfficiency: efficiency,
    };
  }
}
