/**
 * Conversation.ts
 * 
 * 该文件实现了VSCode扩展中的对话功能核心类，负责管理用户与AI助手之间的交互。
 * Conversation类处理对话状态管理、消息交换、模板渲染、AI响应处理以及编辑器内容的差异比较和更新。
 * 它支持多种交互模式，包括消息交换和指令精炼，并提供了导出对话内容、处理错误等功能。
 */

import { webviewApi } from "@codebeat/common";
import Handlebars from "handlebars";
import * as vscode from "vscode";
import { AIClient } from "../ai/AIClient";
import { DiffEditor } from "../diff/DiffEditor";
import { DiffEditorManager } from "../diff/DiffEditorManager";
import { ErrorHandler } from "../utils/ErrorHandler";
import { PerformanceMonitor } from "../utils/PerformanceMonitor";
import { DiffData } from "./DiffData";
import { resolveVariables } from "./input/resolveVariables";
import { executeRAG } from "./retrieval-augmentation/simpleRAG";
import { ConversationTemplate, Prompt } from "./template/ConversationTemplate";

/**
 * 注册Handlebars模板引擎的辅助函数
 * 用于扩展模板功能，添加自定义helper方法
 * 这些辅助函数提供了在模板中进行条件比较的能力
 * eq: 等于, neq: 不等于, lt: 小于, gt: 大于, lte: 小于等于, gte: 大于等于
 */
Handlebars.registerHelper({
  eq: (v1, v2) => v1 === v2,
  neq: (v1, v2) => v1 !== v2,
  lt: (v1, v2) => v1 < v2,
  gt: (v1, v2) => v1 > v2,
  lte: (v1, v2) => v1 <= v2,
  gte: (v1, v2) => v1 >= v2,
});

/**
 * 统一的对话状态类型，涵盖消息交换和指令精炼两种模式
 * 
 * 包含以下可能的状态：
 * - userCanReply: 用户可以回复消息
 * - waitingForBotAnswer: 等待AI助手回答
 * - botAnswerStreaming: AI助手正在流式生成回答
 * - userCanRefineInstruction: 用户可以精炼/修改指令
 */
type ConversationState =
  | webviewApi.MessageExchangeContent["state"]
  | { type: "userCanRefineInstruction"; label?: string; responseMessage?: string };

/**
 * Conversation 类
 * 
 * 管理用户与AI助手之间的对话交互，处理消息交换、模板渲染、AI响应处理
 * 以及编辑器内容的差异比较和更新。支持多种交互模式和完成处理器类型。
 */
export class Conversation {
  /** 对话的唯一标识符 */
  readonly id: string;
  /** AI客户端实例，用于与AI服务通信 */
  protected readonly ai: AIClient;
  /** 当前对话状态，控制UI交互流程 */
  protected state: ConversationState;
  /** 当前错误信息，如果有的话 */
  protected error: webviewApi.Error | undefined;
  /** 对话中的消息列表，包含用户和AI的消息 */
  protected readonly messages: webviewApi.Message[];
  /** 更新聊天面板的回调函数 */
  protected readonly updateChatPanel: () => Promise<void>;

  /** 初始化变量，用于模板渲染 */
  protected readonly initVariables: Record<string, unknown>;

  /** 对话使用的模板定义 */
  private readonly template: ConversationTemplate;

  /** 临时编辑器的内容 */
  private temporaryEditorContent: string | undefined;
  /** 临时编辑器的文档对象 */
  private temporaryEditorDocument: vscode.TextDocument | undefined;
  /** 临时编辑器实例 */
  private temporaryEditor: vscode.TextEditor | undefined;

  /** 差异编辑器的内容 */
  private diffContent: string | undefined;
  /** 差异编辑器实例 */
  private diffEditor: DiffEditor | undefined;
  /** 差异数据，包含原始编辑器、文件名和选中的文本范围 */
  private readonly diffData: DiffData | undefined;
  /** 差异编辑器管理器 */
  private readonly diffEditorManager: DiffEditorManager;

  /**
   * 构造函数，初始化对话实例
   * @param id - 对话的唯一标识符
   * @param initVariables - 初始变量集合
   * @param ai - AI客户端实例
   * @param updateChatPanel - 更新聊天面板的回调函数
   * @param template - 对话模板配置
   * @param diffEditorManager - 差异编辑器管理器
   * @param diffData - 差异数据（可选）
   * @description 
   * - 根据对话模板(conversation template)配置初始化对话状态
   * - 若对话模板包含初始消息，则状态设为等待AI回复
   * - 否则设为允许用户直接回复的状态
   */
  constructor({
    id,
    initVariables,
    ai,
    updateChatPanel,
    template,
    diffEditorManager,
    diffData,
  }: {
    id: string;
    initVariables: Record<string, unknown>;
    ai: AIClient;
    updateChatPanel: () => Promise<void>;
    template: ConversationTemplate;
    diffEditorManager: DiffEditorManager;
    diffData: DiffData | undefined;
  }) {
    this.id = id;
    this.initVariables = initVariables;
    this.ai = ai;
    this.updateChatPanel = updateChatPanel;

    this.template = template;
    this.diffEditorManager = diffEditorManager;
    this.diffData = diffData;

    // 初始化对话中的消息寄存列表，包含:User 和 Bot的消息
    // 消息体数据结构为:
    // {
    //   author: "user" | "bot";
    //   content: string;
    // }
    this.messages = [];

    // 根据模板是否有初始消息来设置初始状态
    this.state =
      template?.initialMessage == null
        ? { type: "userCanReply", responsePlaceholder: undefined } // 如果没有初始消息，用户可以直接回复
        : {
          type: "waitingForBotAnswer", // 如果有初始消息，等待AI助手回答
          botAction: template.initialMessage.placeholder ?? "Answering"
        };
  }

  /**
   * 获取对话的标题
   * 
   * 根据模板配置，可能返回：
   * 1. 第一条消息的内容（如果配置为使用第一条消息作为标题）
   * 2. 通过模板引擎渲染的标题
   * 3. 原始标题（如果渲染失败）
   * 
   * @returns 对话的标题
   */
  async getTitle() {
    const header = this.template.header;

    try {
      const firstMessageContent = this.messages[0]?.content;

      // 如果配置为使用第一条消息作为标题，且第一条消息存在
      // TODO 超长标题问题。根据ConversationHeader可用宽度计算或交由固定长度，通过Tips提示处理。
      if (header.useFirstMessageAsTitle === true && firstMessageContent != null) {
        return firstMessageContent;
      }

      // 否则通过模板引擎渲染标题
      // TODO 超长标题问题。根据ConversationHeader可用宽度计算或交由固定长度，通过Tips提示处理。
      return await this.evaluateTemplate(header.title);
    } catch (error: unknown) {
      // 如果渲染失败，记录错误并返回原始标题
      await ErrorHandler.handleError(
        error,
        'Conversation.getTitle',
        { showToUser: false, logLevel: 'warn' }
      );
      return header.title; // not evaluated
    }
  }

  /**
   * 检查第一条消息是否被用作标题
   * 
   * @returns 如果第一条消息被用作标题且消息列表不为空，则返回true
   */
  isTitleMessage(): boolean {
    return this.template.header.useFirstMessageAsTitle ?? false
      ? this.messages.length > 0
      : false;
  }

  /**
   * 获取对话的图标代码
   * 
   * @returns 表示图标的字符串代码
   */
  getCodicon(): string {
    return this.template.header.icon.value;
  }

  /**
   * 将当前提示信息插入到新的编辑器中
   * 
   * 根据对话状态选择合适的提示模板，渲染后在新的Markdown编辑器中显示
   */
  async insertPromptIntoEditor(): Promise<void> {
    // 选择合适的提示模板：如果是对话开始且有初始消息则使用初始消息，
    // 否则使用*.cbt.md模板中定义的Response模板
    const prompt =
      this.messages[0] == null && this.template.initialMessage != null
        ? this.template.initialMessage
        : this.template.response;
    const variables = await this.resolveVariablesAtMessageTime();
    const text = await this.evaluateTemplate(prompt.template, variables);
    // 创建并显示新的Markdown文档
    const document = await vscode.workspace.openTextDocument({
      language: "markdown",
      content: text,
    });
    await vscode.window.showTextDocument(document);
  }

  /**
   * 将对话内容导出为Markdown格式并在新编辑器中显示
   */
  async exportMarkdown(): Promise<void> {
    const document = await vscode.workspace.openTextDocument({
      language: "markdown",
      content: this.getMarkdownExport(),
    });
    await vscode.window.showTextDocument(document);
  }

  /**
   * 将对话内容转换为Markdown格式的字符串
   * 
   * 将每条消息转换为带有适当标题的Markdown格式，
   * 用户消息标记为"Question"，AI响应标记为"Answer"
   * 
   * @returns Markdown格式的对话内容
   */
  private getMarkdownExport(): string {
    return this.messages
      .flatMap(({ author, content }) => [
        author === "bot" ? "# Answer" : "# Question",
        content,
      ])
      .join("\n\n");
  }

  /**
   * 解析当前消息时刻的变量
   * 
   * @returns 解析后的变量对象
   */
  private async resolveVariablesAtMessageTime() {
    return resolveVariables(this.template.variables, {
      time: "message",
      messages: this.messages,
    });
  }

  /**
   * 使用Handlebars模板引擎渲染对话模板
   * 
   * @param template 对话模板内容
   * @param variables 可选的变量对象，用于模板渲染
   * @returns 渲染后的对话内容
   */
  private async evaluateTemplate(
    template: string,
    variables?: Record<string, unknown>
  ): Promise<string> {
    if (variables == null) {
      variables = await this.resolveVariablesAtMessageTime();
    }

    // 如果存在临时编辑器内容，将其作为特殊变量添加到变量对象中
    if (this.temporaryEditorContent != undefined) {
      variables.temporaryEditorContent = this.temporaryEditorContent;
    }

    // 使用 Handlebars 模板引擎编译传入的 template 字符串
    return Handlebars.compile(template, {
      // 编译时禁用 HTML 特殊字符的自动转义
      noEscape: true,
    })({
      // 根据Handlebars.registerHelper预定义的合并规则，
      // 合并类中预定义的初始变量和调用时传入的动态变量
      ...this.initVariables,
      ...variables,
    });
  }

  /**
   * 执行与AI的对话交互
   * 
   * 该方法负责：
   * 1. 准备提示和变量
   * 2. 执行检索增强（如果配置了）
   * 3. 调用AI客户端获取流式响应
   * 4. 处理响应流和完成事件
   * 5. 错误处理
   */
  private async executeChat() {
    try {
      // 选择合适的提示模板：如果是对话开始且有初始消息则使用初始消息，否则使用响应模板
      const prompt =
        this.messages[0] == null && this.template.initialMessage != null
          ? this.template.initialMessage
          : this.template.response;

      // 解析变量，并使用性能监控包装
      const variables = await PerformanceMonitor.timeAsync(
        'conversation-resolve-variables',
        () => this.resolveVariablesAtMessageTime(),
        { conversationId: this.id, templateId: this.template.id }
      );

      // 如果提示配置了检索增强功能，执行检索增强
      const retrievalAugmentation = prompt.retrievalAugmentation;
      if (retrievalAugmentation != null) {
        variables[retrievalAugmentation.variableName] =
          await PerformanceMonitor.timeAsync(
            'conversation-retrieval-augmentation',
            () => executeRAG({
              retrievalAugmentation,
              variables,
              initVariables: this.initVariables,
              ai: this.ai,
            }),
            { conversationId: this.id, templateId: this.template.id }
          );
      }

      // 调用AI客户端获取流式响应
      const stream = await PerformanceMonitor.timeAsync(
        'conversation-ai-stream-text',
        async () => this.ai.chat({
          prompt: await this.evaluateTemplate(prompt.template, variables),
          maxTokens: prompt.maxTokens,
          stop: prompt.stop,
          temperature: prompt.temperature,
        }),
        { conversationId: this.id, templateId: this.template.id, maxTokens: prompt.maxTokens }
      );

      // 处理流式响应
      let responseUntilNow = "";
      for await (const chunk of stream) {
        responseUntilNow += chunk;
        this.handleCompletion(responseUntilNow, prompt);
      }

      // 处理完整响应（允许清理操作）
      await this.handleCompletion(responseUntilNow, prompt, true);

    } catch (error: unknown) {
      // 错误处理
      await ErrorHandler.handleError(
        error,
        'Conversation.executeChat',
        {
          showToUser: false,
          logLevel: 'error',
          userMessage: 'Failed to process AI response'
        }
      );
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      await this.setError(errorMessage);
    }
  }

  /**
   * 处理LLM的completion返回结果
   * 根据不同类型的handler,对completion返回的内容片段，执行相应逻辑
   * 
   * @param completionContent - LLM返回的原始内容片段
   * @param prompt - 用于从对话模板中提示词(prompt)对象中获取包含completionHandler配置
   * @param completed - 标记completion是否已完成，未完成时进行部分更新
   * 
   * @remarks
   * 支持三种处理器类型：
   * 1. "update-temporary-editor" - 更新临时编辑器内容
   * 2. "active-editor-diff" - 更新差异编辑器内容
   * 3. "message" - 处理对话消息内容
   * 
   * @throws Error - 当传入的prompt所配置的completionHandler为未知处理器类型时抛出错误
   */
  private async handleCompletion(
    completionContent: string,
    prompt: Prompt,
    completed?: boolean | false
  ) {
    // 获取completion处理器，默认为: 对话消息处理器(即,message)
    const completionHandler = prompt.completionHandler ?? {
      type: "message",
    };
    // 获取completion处理器类型，默认为: 对话消息类型(即,message)
    const completionHandlerType = completionHandler.type;

    // 去除补全内容中的前导和尾随空格
    const trimmedCompletion = completionContent.trim();

    // 根据完成处理器类型执行不同的操作
    switch (completionHandlerType) {
      case "update-temporary-editor": {
        // 更新临时编辑器内容
        this.temporaryEditorContent = trimmedCompletion;

        if (completed) {
          // 添加机器人消息
          await this.addBotMessage({
            content: completionHandler.botMessage,
          });
        }

        // 更新临时编辑器
        const language = completionHandler.language;
        await this.updateTemporaryEditor(
          language != null ? await this.evaluateTemplate(language) : undefined
        );
        break;
      }
      case "active-editor-diff": {
        // 更新差异编辑器内容
        this.diffContent = trimmedCompletion;

        if (completed) {
          // 添加机器人消息
          await this.addBotMessage({
            content: "Edit generated",
            responsePlaceholder: "Describe how you want to change the code…",
          });
        }

        // 更新差异编辑器
        await this.updateDiff();
        break;
      }
      case "message": {
        if (completed) {
          // 添加机器人消息
          await this.addBotMessage({
            content: trimmedCompletion,
          });
        } else {
          // 更新部分机器人消息
          await this.updatePartialBotMessage({
            content: trimmedCompletion,
          });
        }
        break;
      }
      default: {
        // 防止上游新增或修改了completion类型的支持范围，
        // 但在当前函数中未被处理，通过穷尽性检查(never特性)，主动引发错误报告，
        // 以确保未被支持的completion类型能被发现。
        const exhaustiveCheck: never = completionHandlerType;
        throw new Error(`Unsupported completion handler type: ${exhaustiveCheck}`);
      }
    }
  }

  /**
   * 更新临时编辑器的内容
   * 
   * 如果临时编辑器不存在，则创建一个新的编辑器。
   * 如果已存在，则智能更新内容，只替换有变化的部分。
   * 
   * @param language 编辑器的语言标识符
   */
  private async updateTemporaryEditor(language: string | undefined) {
    const temporaryEditorContent = this.temporaryEditorContent;

    if (temporaryEditorContent == undefined) {
      return;
    }

    // 确保文档对象已定义
    const temporaryEditorDocument =
      this.temporaryEditorDocument ??
      (await vscode.workspace.openTextDocument({
        language,
        content: temporaryEditorContent,
      }));

    this.temporaryEditorDocument = temporaryEditorDocument;

    if (this.temporaryEditor == undefined) {
      // 如果编辑器不存在，创建一个新的编辑器
      this.temporaryEditor = await vscode.window.showTextDocument(
        temporaryEditorDocument
      );
    } else {
      // 如果编辑器已存在，更新内容
      const currentText = this.temporaryEditor.document.getText();

      // 计算当前文本和新文本的共同前缀长度
      let commonPrefix = 0;
      for (let i = 0; i < currentText.length; i++) {
        if (currentText[i] !== temporaryEditorContent[i]) {
          break;
        }
        commonPrefix++;
      }

      // 只替换有变化的部分，提高更新效率
      this.temporaryEditor.edit((edit: vscode.TextEditorEdit) => {
        edit.replace(
          new vscode.Range(
            temporaryEditorDocument.positionAt(commonPrefix),
            temporaryEditorDocument.positionAt(
              temporaryEditorDocument.getText().length
            )
          ),
          temporaryEditorContent.substring(commonPrefix)
        );
      });
    }
  }

  /**
   * 更新差异编辑器的内容
   * 
   * 处理AI生成的编辑内容，创建差异视图，并设置应用差异的处理程序。
   * 该方法会保持原始代码的缩进风格，确保生成的代码与原始代码风格一致。
   */
  private async updateDiff() {
    const editContent = this.diffContent;
    const diffData = this.diffData;

    if (editContent == undefined || diffData == undefined) {
      return;
    }

    // 获取原始文件内容和选中区域前后的内容
    const document = diffData.editor.document;
    const originalContent = document.getText();
    const prefix = originalContent.substring(
      0,
      document.offsetAt(diffData.range.start)
    );
    const suffix = originalContent.substring(
      document.offsetAt(diffData.range.end)
    );

    // 计算选中文本中每行的最小前导空白字符数
    const minLeadingWhitespace = diffData.selectedText
      .split("\n")
      .map((line) => line.match(/^\s*/)?.[0] ?? "")
      .filter((line) => line.length > 0)
      .reduce((min, line) => Math.min(min, line.length), Infinity);

    // 计算新文本中每行的最小前导空白字符数
    const minLeadingWhitespaceNew = editContent
      .split("\n")
      .map((line) => line.match(/^\s*/)?.[0] ?? "")
      .filter((line) => line.length > 0)
      .reduce((min, line) => Math.min(min, line.length), Infinity);

    // 调整新文本的缩进，使其与原始文本的缩进风格一致
    const editContentWithAdjustedWhitespace = editContent
      .split("\n")
      .map((line) => {
        const leadingWhitespace = line.match(/^\s*/)?.[0] ?? "";
        const relativeIndent =
          leadingWhitespace.length - minLeadingWhitespaceNew;
        const newIndent = Math.max(0, minLeadingWhitespace + relativeIndent);
        return (
          (newIndent < Infinity ? " ".repeat(newIndent) : "") +
          line.substring(leadingWhitespace.length)
        );
      })
      .join("\n");

    // 组合前缀、调整后的编辑内容和后缀，生成编辑后的文件内容
    const editedFileContent = `${prefix}${editContentWithAdjustedWhitespace}${suffix}`;

    // 如果差异编辑器不存在，创建一个新的差异编辑器
    if (this.diffEditor == undefined) {
      this.diffEditor = this.diffEditorManager.createDiffEditor({
        title: `${this.template.label} (${diffData.filename})`,
        editorColumn: diffData.editor.viewColumn ?? vscode.ViewColumn.One,
        conversationId: this.id,
      });
    }

    // 设置差异编辑器的消息处理程序
    this.diffEditor.onDidReceiveMessage(async (rawMessage) => {
      const message = webviewApi.outgoingMessageSchema.parse(rawMessage);

      // 处理错误报告消息
      if (message.type === "reportError") {
        this.setError(message.error);
        return;
      }

      // 处理应用差异消息，应用差异到原始编辑器
      if (message.type !== "applyDiff") return;//非应用差异消息，忽略
      // 创建工作区编辑，替换选中区域的内容
      const edit = new vscode.WorkspaceEdit();
      edit.replace(
        document.uri,
        diffData.range,
        editContentWithAdjustedWhitespace
      );
      await vscode.workspace.applyEdit(edit);

      // 关闭差异编辑器标签页
      const tabGroups = vscode.window.tabGroups;
      const allTabs: vscode.Tab[] = tabGroups.all
        .map((tabGroup) => tabGroup.tabs)
        .flat();

      const tab = allTabs.find((tab) => {
        const tabInput = tab.input as { viewType?: string };
        return (
          tabInput.viewType ===
          `mainThreadWebview-codebeat.diff.${this.id}`
        );
      });

      if (tab != undefined) {
        await tabGroups.close(tab);
      }

      this.diffEditor = undefined;
    });

    // 更新差异编辑器的内容
    await this.diffEditor.updateDiff({
      oldCode: originalContent,
      newCode: editedFileContent,
      languageId: document.languageId,
    });
  }

  /**
   * 重试当前对话
   * 
   * 清除错误状态并重新执行对话，用于处理失败的对话尝试
   */
  async retry() {
    this.state = { type: "waitingForBotAnswer", botAction: undefined };
    await this.dismissError();
    await this.executeChat();
  }

  /**
   * 处理用户的回答
   * 
   * @param userMessage 可选的用户消息内容
   */
  async answer(userMessage?: string) {
    if (userMessage != undefined) {
      await this.addUserMessage({ content: userMessage });
    }

    await this.executeChat();
  }

  /**
   * 添加用户消息到对话
   * 
   * @param options.content 消息内容
   * @param options.botAction 可选的机器人动作描述
   */
  protected async addUserMessage({
    content,
    botAction,
  }: {
    content: string;
    botAction?: string;
  }) {
    this.messages.push({ author: "user", content });
    this.state = { type: "waitingForBotAnswer", botAction };
    await this.updateChatPanel();
  }

  /**
   * 添加机器人消息到对话
   * 
   * @param options.content 消息内容
   * @param options.responsePlaceholder 可选的响应占位符
   */
  protected async addBotMessage({
    content,
    responsePlaceholder,
  }: {
    content: string;
    responsePlaceholder?: string;
  }) {
    this.messages.push({ author: "bot", content });
    this.state = { type: "userCanReply", responsePlaceholder };
    await this.updateChatPanel();
  }

  /**
   * 更新机器人的部分响应消息
   * 用于处理流式响应时的实时更新
   * 
   * @param options.content 部分响应内容
   */
  protected async updatePartialBotMessage({ content }: { content: string }) {
    this.state = { type: "botAnswerStreaming", partialAnswer: content };
    await this.updateChatPanel();
  }

  /**
   * 设置错误状态
   * 
   * @param error 错误信息
   */
  private async setError(error: webviewApi.Error) {
    this.error = error;
    await this.updateChatPanel();
  }

  /**
   * 清除错误状态
   */
  async dismissError() {
    this.error = undefined;
    await this.updateChatPanel();
  }


  /**
   * 将当前会话转换为Webview可用的会话格式
   * 
   * @returns 返回一个Promise，解析为符合webviewApi.Conversation接口的对象
   * @description 根据聊天界面类型("message-exchange"或"instructionRefinement")，
   *              返回不同格式的会话内容。如果第一条消息是标题，则从第二条消息开始返回。
   */
  async toWebviewConversation(): Promise<webviewApi.Conversation> {
    const chatInterface = this.template.chatInterface ?? "message-exchange";

    return {
      id: this.id,
      header: {
        title: await this.getTitle(),
        isTitleMessage: this.isTitleMessage(),
        codicon: this.getCodicon(),
      },
      content:
        chatInterface === "message-exchange"
          ? {
            type: "messageExchange",
            messages: this.isTitleMessage()
              ? this.messages.slice(1)  // 如果第一条消息是标题，则从第二条开始
              : this.messages,
            state: this.getMessageExchangeState(),
            error: this.error,
          }
          : {
            type: "instructionRefinement",
            instruction: "", // TODO last user message?
            state: this.refinementInstructionState(),
            error: this.error,
          },
    };
  }

  /**
   * 从持久化状态安全地恢复对话数据
   * 
   * 该方法为持久化管理器提供对内部状态的受控访问。
   * 即使恢复过程中出现错误，也能保持对话在安全状态。
   * 
   * @param persistedData 持久化的对话数据
   * @param persistedData.messages 消息列表
   * @param persistedData.state 对话状态
   * @param persistedData.error 可选的错误信息
   * @param persistedData.createdAt 可选的创建时间戳
   */
  restoreFromPersisted(persistedData: {
    messages: any[];
    state: any;
    error?: any;
    createdAt?: number;
  }): void {
    try {
      // 清除并恢复消息
      this.messages.length = 0;
      this.messages.push(...persistedData.messages);

      // 恢复状态
      this.state = persistedData.state;

      // 如果存在错误信息，则恢复
      if (persistedData.error !== undefined) {
        this.error = persistedData.error;
      }

      // 如果存在创建时间戳，则恢复
      if (persistedData.createdAt !== undefined) {
        (this as any).createdAt = persistedData.createdAt;
      }
    } catch (error) {
      console.warn(`Failed to restore conversation data:`, error);
      // 即使恢复失败，也保持对话在安全状态
    }
  }

  /**
   * 获取消息交互界面的状态
   * 
   * 将内部对话状态转换为消息交换界面所需的状态格式。
   * 如果当前状态是指令精炼状态，则转换为对应的消息交换状态。
   * 
   * @returns 消息交换界面的状态
   */
  /**
   * 获取消息交换状态
   * 
   * 根据当前状态类型返回对应的消息交换状态：
   * - 对于用户可回复、等待机器人回复、机器人回复流式传输状态，保持原状态不变
   * - 对于用户可优化指令状态，转换为用户可回复状态
   * - 其他未处理状态会抛出类型检查错误
   * 
   * @returns 转换后的消息交换状态内容
   * @throws 当遇到未处理的类型时会抛出错误
   */
  private getMessageExchangeState(): webviewApi.MessageExchangeContent["state"] {
    const { type } = this.state;
    switch (type) {
      case "userCanReply":
      case "waitingForBotAnswer":
      case "botAnswerStreaming":
        // 这些状态在消息交换界面中保持不变
        return this.state;

      case "userCanRefineInstruction":
        // 将指令精炼状态转换为消息交换状态
        return { type: "userCanReply", responsePlaceholder: undefined };

      default: {
        // 类型检查，确保所有类型都被处理
        const exhaustiveCheck: never = type;
        throw new Error(`unsupported type: ${exhaustiveCheck}`);
      }
    }
  }

  /**
   * 将当前会话状态转换为指令精炼界面所需的状态格式
   * 
   * @returns 返回适配指令精炼界面的状态对象：
   * - 当原状态为`botAnswerStreaming`或`waitingForBotAnswer`时返回等待状态
   * - 当原状态为`userCanReply`时转换为可进一步与Bot沟通状态
   * - 当原状态已经是`userCanRefineInstruction`时保持原状态
   * - 其他未处理状态会抛出类型错误
   */
  private refinementInstructionState(): webviewApi.InstructionRefinementContent["state"] {
    const { type } = this.state;
    switch (type) {
      case "botAnswerStreaming":
      case "waitingForBotAnswer":
        // 这些状态在指令精炼界面中都表示为等待机器人回答
        return {
          type: "waitingForBotAnswer",
          botAction: undefined,
        };

      case "userCanReply":
        // 将用户可回复状态转换为用户可精炼指令状态
        return {
          type: "userCanRefineInstruction",
          label: undefined,
          responseMessage: undefined,
        };

      case "userCanRefineInstruction":
        // 已经是指令精炼状态，保持不变
        return {
          type: "userCanRefineInstruction",
          label: (this.state as any).label || undefined,
          responseMessage: (this.state as any).responseMessage || undefined,
        };

      default: {
        // 类型检查，确保所有类型都被处理
        const exhaustiveCheck: never = type;
        throw new Error(`unsupported type: ${exhaustiveCheck}`);
      }
    }
  }
}