# Class Hierarchy Diagram

This template analyzes code and generates a comprehensive class hierarchy diagram. It identifies classes, interfaces, inheritance relationships, and other object-oriented structures with detailed relationship mapping.

## Template

### Configuration

```json conversation-template
{
  "id": "class-hierarchy-diagram",
  "engineVersion": 0,
  "label": "Generate Class Hierarchy Diagram",
  "tags": ["fun","diagram", "code-analysis"],
  "description": "Generate a detailed class hierarchy diagram from your code with inheritance, implementation, and other relationships",
  "header": {
    "title": "Class Hierarchy Diagram ({{location}})",
    "icon": {
      "type": "codicon",
      "value": "symbol-class"
    }
  },
  "variables": [
    {
      "name": "selectedText",
      "time": "conversation-start",
      "type": "selected-text",
      "constraints": [{ "type": "text-length", "min": 1 }]
    },
    {
      "name": "location",
      "time": "conversation-start",
      "type": "selected-location-text"
    },
    {
      "name": "lastMessage",
      "time": "message",
      "type": "message",
      "property": "content",
      "index": -1
    },
    {
      "name": "botRole",
      "time": "conversation-start",
      "type": "constant",
      "value": "class diagram generator"
    }
  ],
  "initialMessage": {
    "placeholder": "Analyzing class structure...",
    "maxTokens": 1024,
    "temperature": 0.3
  },
  "response": {
    "maxTokens": 2048,
    "stop": ["Class Diagram Generator:", "Developer:"],
    "temperature": 0.3
  }
}
```

### Initial Message Prompt

```template-initial-message
## Instructions
You are a {{botRole}}.
Analyze the code below and generate a comprehensive class hierarchy diagram.

## Selected Code
```
{{selectedText}}
```

## Task
You are a {{botRole}}.
1. Identify all classes, interfaces, abstract classes, and enums in the code.
2. Analyze and categorize all relationships between classes:
   - Inheritance (extends) relationships
   - Implementation (implements) relationships
   - Composition relationships (strong "has-a")
   - Aggregation relationships (weak "has-a")
   - Association relationships (uses)
3. Generate a class hierarchy diagram using Mermaid class diagram syntax with:
   - Proper class/interface/abstract class/enum notation
   - Appropriate relationship arrows (inheritance: --|>, implementation: ..|>, composition: --*, aggregation: --o, association: -->)
   - Key fields and methods for each class (focus on public API and important members)
   - Access modifiers (+ public, - private, # protected)
4. For complex diagrams with many classes:
   - Group related classes together
   - Consider breaking into multiple focused diagrams if necessary
5. Explain the key components and relationships in the diagram.
6. If the code is not object-oriented or doesn't contain classes, explain the structure and suggest how it might be represented in a diagram.

## Description

```

### Response Prompt

```template-response
## Instructions
You are a {{botRole}}.
Continue the conversation about the class hierarchy diagram.

## Current Request
Developer: {{lastMessage}}

{{#if selectedText}}
## Selected Code
```
{{selectedText}}
```
{{/if}}

## Conversation
{{#each messages}}
{{#if (eq author "bot")}}
{{botRole}}: {{content}}
{{else}}
Developer: {{content}}
{{/if}}
{{/each}}

## Task
You are a {{botRole}}.
1. Respond to the developer's request about the class hierarchy diagram.
2. If asked to modify or enhance the diagram:
   - Use proper Mermaid class diagram syntax
   - Maintain consistent notation for different relationship types
   - Consider diagram readability and organization
3. When explaining the diagram:
   - Highlight inheritance hierarchies and their significance
   - Explain composition vs. aggregation relationships
   - Identify key design patterns if present
   - Note any potential design issues (e.g., deep inheritance, diamond problem)
4. If new code is provided:
   - Analyze it and update the diagram accordingly
   - Highlight changes from the previous version
5. For language-specific features:
   - Handle multiple inheritance (C++, Python)
   - Represent interfaces with default methods (Java 8+)
   - Show extension methods (.NET)
   - Represent mixins/traits (PHP, Scala, Ruby)

## Response
{{botRole}}:
```